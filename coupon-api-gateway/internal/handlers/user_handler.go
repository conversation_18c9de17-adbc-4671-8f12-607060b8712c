package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type UserHandler struct {
	userClient *clients.UserClient
	logger     *logging.Logger
}

func NewUserHandler(u *clients.UserClient, l *logging.Logger) *UserHandler {
	return &UserHandler{userClient: u, logger: l}
}

func (h *UserHandler) RegisterProtectedRoutes(g *echo.Group) {
	g.GET("/users/me", h.HandleGetMe)
}

func (h *UserHandler) HandleGetMe(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	res, err := h.userClient.GetUser(c.Request().Context(), userID)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	userResponse := dto.ToUserResponse(res.User)

	return c.JSON(http.StatusOK, userResponse)
}
