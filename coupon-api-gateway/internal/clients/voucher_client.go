package clients

import (
	"context"
	"fmt"
	"time"

	voucher_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type VoucherClient struct {
	Client voucher_proto_v1.VoucherServiceClient
	conn   *shared_grpc.Client
}

func NewVoucherClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*VoucherClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for voucher service: %w", err)
	}
	return &VoucherClient{
		Client: voucher_proto_v1.NewVoucherServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *VoucherClient) Close() {
	c.conn.Close()
}

// CRUD operations
func (c *VoucherClient) CreateVoucher(ctx context.Context, req *voucher_proto_v1.CreateVoucherRequest) (*voucher_proto_v1.CreateVoucherResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.CreateVoucher(ctx, req)
}

func (c *VoucherClient) GetVoucher(ctx context.Context, req *voucher_proto_v1.GetVoucherRequest) (*voucher_proto_v1.GetVoucherResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetVoucher(ctx, req)
}

func (c *VoucherClient) GetVoucherByCode(ctx context.Context, req *voucher_proto_v1.GetVoucherByCodeRequest) (*voucher_proto_v1.GetVoucherByCodeResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetVoucherByCode(ctx, req)
}

func (c *VoucherClient) UpdateVoucher(ctx context.Context, req *voucher_proto_v1.UpdateVoucherRequest) (*voucher_proto_v1.UpdateVoucherResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.UpdateVoucher(ctx, req)
}

func (c *VoucherClient) ListVouchers(ctx context.Context, req *voucher_proto_v1.ListVouchersRequest) (*voucher_proto_v1.ListVouchersResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return c.Client.ListVouchers(ctx, req)
}

// Discount types
func (c *VoucherClient) GetDiscountTypes(ctx context.Context, req *voucher_proto_v1.GetDiscountTypesRequest) (*voucher_proto_v1.GetDiscountTypesResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetDiscountTypes(ctx, req)
}

// Eligibility and application
func (c *VoucherClient) CheckVoucherEligibility(ctx context.Context, req *voucher_proto_v1.CheckVoucherEligibilityRequest) (*voucher_proto_v1.CheckVoucherEligibilityResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.CheckVoucherEligibility(ctx, req)
}

func (c *VoucherClient) ListAutoEligibleVouchers(ctx context.Context, req *voucher_proto_v1.ListAutoEligibleVouchersRequest) (*voucher_proto_v1.ListAutoEligibleVouchersResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.ListAutoEligibleVouchers(ctx, req)
}
