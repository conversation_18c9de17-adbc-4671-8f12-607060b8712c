package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/repository"

	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type VoucherService interface {
	CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error)
	ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error)
}

type voucherService struct {
	repo       repository.VoucherRepository
	userClient *clients.UserClient
	logger     *logging.Logger
}

func NewVoucherService(repo repository.VoucherRepository, userClient *clients.UserClient, logger *logging.Logger) VoucherService {
	return &voucherService{repo: repo, userClient: userClient, logger: logger}
}

func (s *voucherService) CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error) {
	voucher, err := s.repo.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		return nil, errors.NewNotFoundError(fmt.Sprintf("coupon '%s' not found", req.VoucherCode))
	}

	if voucher.Status != "ACTIVE" {
		return &proto_voucher_v1.CheckVoucherEligibilityResponse{Eligible: false, Message: "Coupon is not active."}, nil
	}
	if time.Now().Before(voucher.ValidFrom) || time.Now().After(voucher.ValidUntil) {
		return &proto_voucher_v1.CheckVoucherEligibilityResponse{Eligible: false, Message: "Coupon is expired or not yet valid."}, nil
	}
	if req.OrderAmount < voucher.MinOrderAmount {
		return &proto_voucher_v1.CheckVoucherEligibilityResponse{Eligible: false, Message: "Minimum order amount not met."}, nil
	}
	if voucher.MaxUsageCount != nil && voucher.CurrentUsageCount >= *voucher.MaxUsageCount {
		return &proto_voucher_v1.CheckVoucherEligibilityResponse{Eligible: false, Message: "Coupon usage limit exceeded."}, nil
	}

	discountAmount := calculateDiscount(voucher, req.OrderAmount)

	return &proto_voucher_v1.CheckVoucherEligibilityResponse{
		Eligible:       true,
		Message:        "Coupon is eligible.",
		VoucherId:      voucher.ID,
		DiscountAmount: discountAmount,
	}, nil
}

func (s *voucherService) ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error) {
	eligibleVouchers, err := s.repo.GetActiveAutoVouchers(ctx, req.OrderAmount)
	if err != nil {
		return nil, errors.NewInternalError("failed to retrieve eligible coupons")
	}

	var responseVouchers []*proto_voucher_v1.EligibleVoucherInfo
	for _, voucher := range eligibleVouchers {
		discountAmount := calculateDiscount(voucher, req.OrderAmount)
		responseVouchers = append(responseVouchers, &proto_voucher_v1.EligibleVoucherInfo{
			Voucher: &proto_voucher_v1.VoucherInfo{
				Id:            voucher.ID,
				VoucherCode:   voucher.VoucherCode,
				DiscountValue: voucher.DiscountValue,
			},
			DiscountAmount: discountAmount,
		})
	}

	return &proto_voucher_v1.ListAutoEligibleVouchersResponse{Vouchers: responseVouchers}, nil
}

func calculateDiscount(voucher *model.Voucher, orderAmount float64) float64 {
	var discount float64
	if voucher.DiscountTypeID != "" {
		discount = orderAmount * (voucher.DiscountValue / 100)
	}

	if voucher.MaxDiscountAmount != nil && discount > *voucher.MaxDiscountAmount {
		discount = *voucher.MaxDiscountAmount
	}

	if discount > orderAmount {
		return orderAmount
	}

	return discount
}
