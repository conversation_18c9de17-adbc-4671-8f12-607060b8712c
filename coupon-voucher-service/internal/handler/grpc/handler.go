package handler

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/service"
)

type VoucherServer struct {
	proto_voucher_v1.UnimplementedVoucherServiceServer
	svc service.VoucherService
}

func NewVoucherServer(svc service.VoucherService) *VoucherServer {
	return &VoucherServer{svc: svc}
}

func (s *VoucherServer) CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error) {
	resp, err := s.svc.CheckEligibility(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *VoucherServer) ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error) {
	resp, err := s.svc.ListEligibleAutoVouchers(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *VoucherServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{Status: proto_common_v1.HealthCheckResponse_SERVING_STATUS_SERVING}, nil
}
