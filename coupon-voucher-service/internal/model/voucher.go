package model

import "time"

type Voucher struct {
	ID                string `gorm:"type:uuid;primary_key;"`
	VoucherCode       string `gorm:"type:varchar(255);not null;unique"`
	Title             string `gorm:"type:varchar(255);not null"`
	Description       string
	DiscountTypeID    string    `gorm:"type:uuid;not null"`
	DiscountValue     float64   `gorm:"not null"`
	UsageMethod       string    `gorm:"type:varchar(50);not null"`
	ValidFrom         time.Time `gorm:"not null"`
	ValidUntil        time.Time `gorm:"not null"`
	MaxUsageCount     *int
	CurrentUsageCount int     `gorm:"not null;default:0"`
	MinOrderAmount    float64 `gorm:"not null;default:0"`
	MaxDiscountAmount *float64
	Status            string `gorm:"type:varchar(50);not null;default:'ACTIVE'"`
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

type DiscountType struct {
	ID          string `gorm:"type:uuid;primary_key;"`
	TypeCode    string `gorm:"type:varchar(50);not null;unique"`
	TypeName    string `gorm:"type:varchar(255);not null"`
	Description string
}

func (Voucher) TableName() string {
	return "coupons"
}

func (DiscountType) TableName() string {
	return "discount_types"
}

func GetAllModels() []any {
	return []any{&Voucher{}, &DiscountType{}}
}
