package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gorm.io/gorm"
)

type VoucherRepository interface {
	// CRUD operations
	Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy string) (*model.Voucher, error)
	GetByID(ctx context.Context, id string) (*model.Voucher, error)
	GetByCode(ctx context.Context, code string) (*model.Voucher, error)
	Update(ctx context.Context, id string, req *model.UpdateVoucherRequest) error
	List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.Voucher, int, error)

	// Discount types
	GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error)

	// Eligibility and auto-apply
	GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error)
	CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error)
	GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.EligibleVoucher, error)

	// Usage tracking
	IncrementUsageCount(ctx context.Context, id string) error

	// Related data operations
	GetUserEligibilityRules(ctx context.Context, voucherID string) ([]*model.VoucherUserEligibility, error)
	ReplaceProductRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherProductRestriction) error
	ReplaceTimeRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherTimeRestriction) error
	ReplaceUserEligibility(ctx context.Context, voucherID string, rules []*model.VoucherUserEligibility) error
}

type voucherRepository struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

func NewVoucherRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) VoucherRepository {
	return &voucherRepository{db: db, redis: redis, logger: logger}
}

// Create creates a new voucher
func (r *voucherRepository) Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy string) (*model.Voucher, error) {
	voucher := &model.Voucher{
		VoucherCode:       req.VoucherCode,
		Title:             req.Title,
		Description:       req.Description,
		DiscountTypeID:    req.DiscountTypeID,
		DiscountValue:     req.DiscountValue,
		UsageMethod:       req.UsageMethod,
		ValidFrom:         req.ValidFrom,
		ValidUntil:        req.ValidUntil,
		MaxUsageCount:     req.MaxUsageCount,
		MaxUsagePerUser:   req.MaxUsagePerUser,
		MinOrderAmount:    req.MinOrderAmount,
		MaxDiscountAmount: req.MaxDiscountAmount,
		CreatedBy:         createdBy,
		Status:            model.VoucherStatusActive,
		UserEligibility:   "ALL",
	}

	if err := r.db.WithContext(ctx).Create(voucher).Error; err != nil {
		return nil, err
	}

	return r.GetByID(ctx, voucher.ID)
}

// GetByID retrieves a voucher by ID with all related data
func (r *voucherRepository) GetByID(ctx context.Context, id string) (*model.Voucher, error) {
	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&voucher).Error; err != nil {
		return nil, err
	}

	// Load related data
	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", id, err)
	}

	return &voucher, nil
}

// GetByCode retrieves a voucher by code
func (r *voucherRepository) GetByCode(ctx context.Context, code string) (*model.Voucher, error) {
	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("voucher_code = ?", code).First(&voucher).Error; err != nil {
		return nil, err
	}

	// Load related data
	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", code, err)
	}

	return &voucher, nil
}

func (r *voucherRepository) GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error) {
	var vouchers []*model.Voucher
	err := r.db.WithContext(ctx).
		Where("usage_method = ?", model.UsageMethodAutomatic).
		Where("status = ?", model.VoucherStatusActive).
		Where("valid_from <= ?", time.Now()).
		Where("valid_until >= ?", time.Now()).
		Where("min_order_amount <= ?", orderAmount).
		Where("max_usage_count IS NULL OR current_usage_count < max_usage_count").
		Find(&vouchers).Error
	return vouchers, err
}

// Helper method to load related data for a voucher
func (r *voucherRepository) loadRelatedData(ctx context.Context, voucher *model.Voucher) error {
	// Load discount type
	var discountType model.DiscountType
	if err := r.db.WithContext(ctx).Where("id = ?", voucher.DiscountTypeID).First(&discountType).Error; err == nil {
		voucher.DiscountType = &discountType
	}

	// Load product restrictions
	var productRestrictions []*model.VoucherProductRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&productRestrictions).Error; err == nil {
		voucher.ProductRestrictions = productRestrictions
	}

	// Load time restrictions
	var timeRestrictions []*model.VoucherTimeRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&timeRestrictions).Error; err == nil {
		voucher.TimeRestrictions = timeRestrictions
	}

	// Load user eligibility rules
	var userEligibility []*model.VoucherUserEligibility
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&userEligibility).Error; err == nil {
		voucher.UserEligibilityRules = userEligibility
	}

	return nil
}

// Update updates a voucher
func (r *voucherRepository) Update(ctx context.Context, id string, req *model.UpdateVoucherRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update main voucher record
		updates := map[string]interface{}{
			"title":               req.Title,
			"description":         req.Description,
			"discount_type_id":    req.DiscountTypeID,
			"discount_value":      req.DiscountValue,
			"usage_method":        req.UsageMethod,
			"status":              req.Status,
			"min_order_amount":    req.MinOrderAmount,
			"max_discount_amount": req.MaxDiscountAmount,
			"max_usage_count":     req.MaxUsageCount,
			"max_usage_per_user":  req.MaxUsagePerUser,
			"valid_from":          req.ValidFrom,
			"valid_until":         req.ValidUntil,
		}

		if err := tx.Model(&model.Voucher{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return err
		}

		// Replace product restrictions
		if err := r.replaceProductRestrictionsInTx(ctx, tx, id, req.ProductRestrictions); err != nil {
			return err
		}

		// Replace time restrictions
		if err := r.replaceTimeRestrictionsInTx(ctx, tx, id, req.TimeRestrictions); err != nil {
			return err
		}

		// Replace user eligibility rules
		if err := r.replaceUserEligibilityInTx(ctx, tx, id, req.UserEligibility); err != nil {
			return err
		}

		return nil
	})
}

// List retrieves vouchers with pagination and filtering
func (r *voucherRepository) List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.Voucher, int, error) {
	var vouchers []*model.Voucher
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Voucher{})

	// Apply filters
	if req.Search != "" {
		query = query.Where("title ILIKE ? OR voucher_code ILIKE ? OR description ILIKE ?",
			"%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if req.DiscountTypeID != nil {
		query = query.Where("discount_type_id = ?", *req.DiscountTypeID)
	}

	if req.UsageMethod != nil {
		query = query.Where("usage_method = ?", *req.UsageMethod)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	if err := query.Find(&vouchers).Error; err != nil {
		return nil, 0, err
	}

	// Load related data for each voucher
	for _, voucher := range vouchers {
		if err := r.loadRelatedData(ctx, voucher); err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", voucher.ID, err)
		}
	}

	return vouchers, int(total), nil
}

// GetDiscountTypes retrieves all discount types
func (r *voucherRepository) GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error) {
	var discountTypes []*model.DiscountType
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&discountTypes).Error
	return discountTypes, err
}

// CheckVoucherEligibility checks if a voucher is eligible for use
func (r *voucherRepository) CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error) {
	// TODO: Implement complex eligibility logic similar to the SQL function
	// For now, return a basic implementation
	voucher, err := r.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher not found",
		}, nil
	}

	if voucher.Status != model.VoucherStatusActive {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher is not active",
		}, nil
	}

	if time.Now().Before(voucher.ValidFrom) || time.Now().After(voucher.ValidUntil) {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher is expired or not yet valid",
		}, nil
	}

	if req.OrderAmount < voucher.MinOrderAmount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Minimum order amount not met",
		}, nil
	}

	if voucher.MaxUsageCount != nil && voucher.CurrentUsageCount >= *voucher.MaxUsageCount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher usage limit exceeded",
		}, nil
	}

	discountAmount := r.calculateDiscount(voucher, req.OrderAmount)

	return &model.VoucherEligibilityResponse{
		Eligible:       true,
		Message:        "Voucher is eligible",
		VoucherID:      &voucher.ID,
		DiscountAmount: discountAmount,
	}, nil
}

// GetEligibleAutoVouchers retrieves eligible auto-apply vouchers
func (r *voucherRepository) GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.EligibleVoucher, error) {
	vouchers, err := r.GetActiveAutoVouchers(ctx, req.OrderAmount)
	if err != nil {
		return nil, err
	}

	var eligibleVouchers []*model.EligibleVoucher
	for _, voucher := range vouchers {
		discountAmount := r.calculateDiscount(voucher, req.OrderAmount)
		eligibleVouchers = append(eligibleVouchers, &model.EligibleVoucher{
			Eligible:       true,
			Voucher:        voucher,
			DiscountAmount: discountAmount,
		})
	}

	return eligibleVouchers, nil
}

// IncrementUsageCount increments the usage count for a voucher
func (r *voucherRepository) IncrementUsageCount(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Model(&model.Voucher{}).
		Where("id = ?", id).
		UpdateColumn("current_usage_count", gorm.Expr("current_usage_count + 1")).Error
}

// GetUserEligibilityRules retrieves user eligibility rules for a voucher
func (r *voucherRepository) GetUserEligibilityRules(ctx context.Context, voucherID string) ([]*model.VoucherUserEligibility, error) {
	var rules []*model.VoucherUserEligibility
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucherID).Find(&rules).Error
	return rules, err
}

// ReplaceProductRestrictions replaces product restrictions for a voucher
func (r *voucherRepository) ReplaceProductRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherProductRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceProductRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

// ReplaceTimeRestrictions replaces time restrictions for a voucher
func (r *voucherRepository) ReplaceTimeRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherTimeRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceTimeRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

// ReplaceUserEligibility replaces user eligibility rules for a voucher
func (r *voucherRepository) ReplaceUserEligibility(ctx context.Context, voucherID string, rules []*model.VoucherUserEligibility) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceUserEligibilityInTx(ctx, tx, voucherID, rules)
	})
}

// Helper methods for transaction-based operations
func (r *voucherRepository) replaceProductRestrictionsInTx(ctx context.Context, tx *gorm.DB, voucherID string, restrictions []*model.VoucherProductRestriction) error {
	// Delete existing restrictions
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherProductRestriction{}).Error; err != nil {
		return err
	}

	// Insert new restrictions
	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceTimeRestrictionsInTx(ctx context.Context, tx *gorm.DB, voucherID string, restrictions []*model.VoucherTimeRestriction) error {
	// Delete existing restrictions
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherTimeRestriction{}).Error; err != nil {
		return err
	}

	// Insert new restrictions
	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceUserEligibilityInTx(ctx context.Context, tx *gorm.DB, voucherID string, rules []*model.VoucherUserEligibility) error {
	// Delete existing rules
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherUserEligibility{}).Error; err != nil {
		return err
	}

	// Insert new rules
	for _, rule := range rules {
		rule.VoucherID = voucherID
		if err := tx.Create(rule).Error; err != nil {
			return err
		}
	}

	return nil
}

// calculateDiscount calculates the discount amount for a voucher
func (r *voucherRepository) calculateDiscount(voucher *model.Voucher, orderAmount float64) float64 {
	var discount float64

	// Get discount type to determine calculation method
	if voucher.DiscountType != nil {
		switch voucher.DiscountType.TypeCode {
		case "PERCENT":
			discount = orderAmount * (voucher.DiscountValue / 100)
		case "FIXED":
			discount = voucher.DiscountValue
		case "FLAT":
			discount = orderAmount - voucher.DiscountValue
			if discount < 0 {
				discount = orderAmount
			}
		default:
			discount = 0
		}
	} else {
		// Default to percentage if discount type is not loaded
		discount = orderAmount * (voucher.DiscountValue / 100)
	}

	// Apply max discount cap if exists
	if voucher.MaxDiscountAmount != nil && discount > *voucher.MaxDiscountAmount {
		discount = *voucher.MaxDiscountAmount
	}

	// Cannot exceed order amount
	if discount > orderAmount {
		discount = orderAmount
	}

	return discount
}
