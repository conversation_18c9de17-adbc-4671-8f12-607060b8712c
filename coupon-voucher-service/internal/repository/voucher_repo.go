package repository

import (
	"context"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
)

type VoucherRepository interface {
	GetByCode(ctx context.Context, code string) (*model.Voucher, error)
	GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error)
}

type voucherRepository struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

func NewVoucherRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) VoucherRepository {
	return &voucherRepository{db: db, redis: redis, logger: logger}
}

func (r *voucherRepository) GetByCode(ctx context.Context, code string) (*model.Voucher, error) {
	var coupon model.Voucher
	if err := r.db.WithContext(ctx).Where("coupon_code = ?", code).First(&coupon).Error; err != nil {
		return nil, err
	}
	return &coupon, nil
}

func (r *voucherRepository) GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error) {
	var coupons []*model.Voucher
	err := r.db.WithContext(ctx).
		Where("usage_method = ?", "AUTO").
		Where("status = ?", "ACTIVE").
		Where("valid_from <= ?", time.Now()).
		Where("valid_until >= ?", time.Now()).
		Where("min_order_amount <= ?", orderAmount).
		Where("max_usage_count IS NULL OR current_usage_count < max_usage_count").
		Find(&coupons).Error
	return coupons, err
}
