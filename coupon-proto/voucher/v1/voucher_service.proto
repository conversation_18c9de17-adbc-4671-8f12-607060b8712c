syntax = "proto3";

package coupon.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1";

service VoucherService {
  rpc CheckVoucherEligibility(CheckVoucherEligibilityRequest)
      returns (CheckVoucherEligibilityResponse);
  rpc ListAutoEligibleVouchers(ListAutoEligibleVouchersRequest)
      returns (ListAutoEligibleVouchersResponse);
  rpc HealthCheck(common.v1.HealthCheckRequest)
      returns (common.v1.HealthCheckResponse);
}

message CartItem {
  string product_id = 1;
  string category_id = 2;
  int32 quantity = 3;
  double price = 4;
}

message CheckVoucherEligibilityRequest {
  common.v1.RequestMetadata metadata = 1;
  string voucher_code = 2;
  string user_id = 3;
  double order_amount = 4;
  google.protobuf.Timestamp order_timestamp = 5;
  repeated CartItem cart_items = 6;
}

message CheckVoucherEligibilityResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool eligible = 2;
  string message = 3;
  string voucher_id = 4;
  double discount_amount = 5;
  common.v1.ServiceError error = 6;
}

message ListAutoEligibleVouchersRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  double order_amount = 3;
  google.protobuf.Timestamp order_timestamp = 4;
  repeated CartItem cart_items = 5;
}

message VoucherInfo {
  string id = 1;
  string voucher_code = 2;
  double discount_value = 3;
  string usage_method = 4;
  string discount_type_id = 5;
}

message EligibleVoucherInfo {
  bool eligible = 1;
  VoucherInfo voucher = 2;
  double discount_amount = 3;
}

message ListAutoEligibleVouchersResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated EligibleVoucherInfo vouchers = 2;
  common.v1.ServiceError error = 3;
}

