// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: voucher/v1/voucher_service.proto

package v1

import (
	context "context"
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VoucherService_CheckVoucherEligibility_FullMethodName  = "/coupon.v1.VoucherService/CheckVoucherEligibility"
	VoucherService_ListAutoEligibleVouchers_FullMethodName = "/coupon.v1.VoucherService/ListAutoEligibleVouchers"
	VoucherService_HealthCheck_FullMethodName              = "/coupon.v1.VoucherService/HealthCheck"
)

// VoucherServiceClient is the client API for VoucherService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoucherServiceClient interface {
	CheckVoucherEligibility(ctx context.Context, in *CheckVoucherEligibilityRequest, opts ...grpc.CallOption) (*CheckVoucherEligibilityResponse, error)
	ListAutoEligibleVouchers(ctx context.Context, in *ListAutoEligibleVouchersRequest, opts ...grpc.CallOption) (*ListAutoEligibleVouchersResponse, error)
	HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error)
}

type voucherServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVoucherServiceClient(cc grpc.ClientConnInterface) VoucherServiceClient {
	return &voucherServiceClient{cc}
}

func (c *voucherServiceClient) CheckVoucherEligibility(ctx context.Context, in *CheckVoucherEligibilityRequest, opts ...grpc.CallOption) (*CheckVoucherEligibilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVoucherEligibilityResponse)
	err := c.cc.Invoke(ctx, VoucherService_CheckVoucherEligibility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) ListAutoEligibleVouchers(ctx context.Context, in *ListAutoEligibleVouchersRequest, opts ...grpc.CallOption) (*ListAutoEligibleVouchersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAutoEligibleVouchersResponse)
	err := c.cc.Invoke(ctx, VoucherService_ListAutoEligibleVouchers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.HealthCheckResponse)
	err := c.cc.Invoke(ctx, VoucherService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoucherServiceServer is the server API for VoucherService service.
// All implementations should embed UnimplementedVoucherServiceServer
// for forward compatibility.
type VoucherServiceServer interface {
	CheckVoucherEligibility(context.Context, *CheckVoucherEligibilityRequest) (*CheckVoucherEligibilityResponse, error)
	ListAutoEligibleVouchers(context.Context, *ListAutoEligibleVouchersRequest) (*ListAutoEligibleVouchersResponse, error)
	HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error)
}

// UnimplementedVoucherServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVoucherServiceServer struct{}

func (UnimplementedVoucherServiceServer) CheckVoucherEligibility(context.Context, *CheckVoucherEligibilityRequest) (*CheckVoucherEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVoucherEligibility not implemented")
}
func (UnimplementedVoucherServiceServer) ListAutoEligibleVouchers(context.Context, *ListAutoEligibleVouchersRequest) (*ListAutoEligibleVouchersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAutoEligibleVouchers not implemented")
}
func (UnimplementedVoucherServiceServer) HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedVoucherServiceServer) testEmbeddedByValue() {}

// UnsafeVoucherServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoucherServiceServer will
// result in compilation errors.
type UnsafeVoucherServiceServer interface {
	mustEmbedUnimplementedVoucherServiceServer()
}

func RegisterVoucherServiceServer(s grpc.ServiceRegistrar, srv VoucherServiceServer) {
	// If the following call pancis, it indicates UnimplementedVoucherServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VoucherService_ServiceDesc, srv)
}

func _VoucherService_CheckVoucherEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVoucherEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).CheckVoucherEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_CheckVoucherEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).CheckVoucherEligibility(ctx, req.(*CheckVoucherEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_ListAutoEligibleVouchers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAutoEligibleVouchersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).ListAutoEligibleVouchers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_ListAutoEligibleVouchers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).ListAutoEligibleVouchers(ctx, req.(*ListAutoEligibleVouchersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).HealthCheck(ctx, req.(*v1.HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VoucherService_ServiceDesc is the grpc.ServiceDesc for VoucherService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VoucherService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "coupon.v1.VoucherService",
	HandlerType: (*VoucherServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckVoucherEligibility",
			Handler:    _VoucherService_CheckVoucherEligibility_Handler,
		},
		{
			MethodName: "ListAutoEligibleVouchers",
			Handler:    _VoucherService_ListAutoEligibleVouchers_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _VoucherService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "voucher/v1/voucher_service.proto",
}
