// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: voucher/v1/voucher_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CartItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductId     string                 `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Price         float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CartItem) Reset() {
	*x = CartItem{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CartItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CartItem) ProtoMessage() {}

func (x *CartItem) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CartItem.ProtoReflect.Descriptor instead.
func (*CartItem) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{0}
}

func (x *CartItem) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CartItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *CartItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CartItem) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type CheckVoucherEligibilityRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherCode    string                 `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,4,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,6,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckVoucherEligibilityRequest) Reset() {
	*x = CheckVoucherEligibilityRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVoucherEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVoucherEligibilityRequest) ProtoMessage() {}

func (x *CheckVoucherEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVoucherEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckVoucherEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{1}
}

func (x *CheckVoucherEligibilityRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CheckVoucherEligibilityRequest) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *CheckVoucherEligibilityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckVoucherEligibilityRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *CheckVoucherEligibilityRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *CheckVoucherEligibilityRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type CheckVoucherEligibilityResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Eligible       bool                   `protobuf:"varint,2,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Message        string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	VoucherId      string                 `protobuf:"bytes,4,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,5,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	Error          *v1.ServiceError       `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckVoucherEligibilityResponse) Reset() {
	*x = CheckVoucherEligibilityResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVoucherEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVoucherEligibilityResponse) ProtoMessage() {}

func (x *CheckVoucherEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVoucherEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckVoucherEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckVoucherEligibilityResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CheckVoucherEligibilityResponse) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *CheckVoucherEligibilityResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckVoucherEligibilityResponse) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *CheckVoucherEligibilityResponse) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *CheckVoucherEligibilityResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListAutoEligibleVouchersRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId         string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,3,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,5,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListAutoEligibleVouchersRequest) Reset() {
	*x = ListAutoEligibleVouchersRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAutoEligibleVouchersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAutoEligibleVouchersRequest) ProtoMessage() {}

func (x *ListAutoEligibleVouchersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAutoEligibleVouchersRequest.ProtoReflect.Descriptor instead.
func (*ListAutoEligibleVouchersRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAutoEligibleVouchersRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListAutoEligibleVouchersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListAutoEligibleVouchersRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *ListAutoEligibleVouchersRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *ListAutoEligibleVouchersRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type VoucherInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	VoucherCode    string                 `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	DiscountValue  float64                `protobuf:"fixed64,3,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	UsageMethod    string                 `protobuf:"bytes,4,opt,name=usage_method,json=usageMethod,proto3" json:"usage_method,omitempty"`
	DiscountTypeId string                 `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3" json:"discount_type_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *VoucherInfo) Reset() {
	*x = VoucherInfo{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherInfo) ProtoMessage() {}

func (x *VoucherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherInfo.ProtoReflect.Descriptor instead.
func (*VoucherInfo) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{4}
}

func (x *VoucherInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VoucherInfo) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *VoucherInfo) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *VoucherInfo) GetUsageMethod() string {
	if x != nil {
		return x.UsageMethod
	}
	return ""
}

func (x *VoucherInfo) GetDiscountTypeId() string {
	if x != nil {
		return x.DiscountTypeId
	}
	return ""
}

type EligibleVoucherInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Eligible       bool                   `protobuf:"varint,1,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Voucher        *VoucherInfo           `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,3,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *EligibleVoucherInfo) Reset() {
	*x = EligibleVoucherInfo{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EligibleVoucherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EligibleVoucherInfo) ProtoMessage() {}

func (x *EligibleVoucherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EligibleVoucherInfo.ProtoReflect.Descriptor instead.
func (*EligibleVoucherInfo) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{5}
}

func (x *EligibleVoucherInfo) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *EligibleVoucherInfo) GetVoucher() *VoucherInfo {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *EligibleVoucherInfo) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

type ListAutoEligibleVouchersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Vouchers      []*EligibleVoucherInfo `protobuf:"bytes,2,rep,name=vouchers,proto3" json:"vouchers,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAutoEligibleVouchersResponse) Reset() {
	*x = ListAutoEligibleVouchersResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAutoEligibleVouchersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAutoEligibleVouchersResponse) ProtoMessage() {}

func (x *ListAutoEligibleVouchersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAutoEligibleVouchersResponse.ProtoReflect.Descriptor instead.
func (*ListAutoEligibleVouchersResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListAutoEligibleVouchersResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListAutoEligibleVouchersResponse) GetVouchers() []*EligibleVoucherInfo {
	if x != nil {
		return x.Vouchers
	}
	return nil
}

func (x *ListAutoEligibleVouchersResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_voucher_v1_voucher_service_proto protoreflect.FileDescriptor

const file_voucher_v1_voucher_service_proto_rawDesc = "" +
	"\n" +
	" voucher/v1/voucher_service.proto\x12\tcoupon.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"|\n" +
	"\bCartItem\x12\x1d\n" +
	"\n" +
	"product_id\x18\x01 \x01(\tR\tproductId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\tR\n" +
	"categoryId\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x01R\x05price\"\xb0\x02\n" +
	"\x1eCheckVoucherEligibilityRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x04 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x122\n" +
	"\n" +
	"cart_items\x18\x06 \x03(\v2\x13.coupon.v1.CartItemR\tcartItems\"\x87\x02\n" +
	"\x1fCheckVoucherEligibilityResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1a\n" +
	"\beligible\x18\x02 \x01(\bR\beligible\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x04 \x01(\tR\tvoucherId\x12'\n" +
	"\x0fdiscount_amount\x18\x05 \x01(\x01R\x0ediscountAmount\x12-\n" +
	"\x05error\x18\x06 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x8e\x02\n" +
	"\x1fListAutoEligibleVouchersRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x03 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x122\n" +
	"\n" +
	"cart_items\x18\x05 \x03(\v2\x13.coupon.v1.CartItemR\tcartItems\"\xb4\x01\n" +
	"\vVoucherInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\x12%\n" +
	"\x0ediscount_value\x18\x03 \x01(\x01R\rdiscountValue\x12!\n" +
	"\fusage_method\x18\x04 \x01(\tR\vusageMethod\x12(\n" +
	"\x10discount_type_id\x18\x05 \x01(\tR\x0ediscountTypeId\"\x8c\x01\n" +
	"\x13EligibleVoucherInfo\x12\x1a\n" +
	"\beligible\x18\x01 \x01(\bR\beligible\x120\n" +
	"\avoucher\x18\x02 \x01(\v2\x16.coupon.v1.VoucherInfoR\avoucher\x12'\n" +
	"\x0fdiscount_amount\x18\x03 \x01(\x01R\x0ediscountAmount\"\xc6\x01\n" +
	" ListAutoEligibleVouchersResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12:\n" +
	"\bvouchers\x18\x02 \x03(\v2\x1e.coupon.v1.EligibleVoucherInfoR\bvouchers\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\xc5\x02\n" +
	"\x0eVoucherService\x12p\n" +
	"\x17CheckVoucherEligibility\x12).coupon.v1.CheckVoucherEligibilityRequest\x1a*.coupon.v1.CheckVoucherEligibilityResponse\x12s\n" +
	"\x18ListAutoEligibleVouchers\x12*.coupon.v1.ListAutoEligibleVouchersRequest\x1a+.coupon.v1.ListAutoEligibleVouchersResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB9Z7gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1b\x06proto3"

var (
	file_voucher_v1_voucher_service_proto_rawDescOnce sync.Once
	file_voucher_v1_voucher_service_proto_rawDescData []byte
)

func file_voucher_v1_voucher_service_proto_rawDescGZIP() []byte {
	file_voucher_v1_voucher_service_proto_rawDescOnce.Do(func() {
		file_voucher_v1_voucher_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_voucher_v1_voucher_service_proto_rawDesc), len(file_voucher_v1_voucher_service_proto_rawDesc)))
	})
	return file_voucher_v1_voucher_service_proto_rawDescData
}

var file_voucher_v1_voucher_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_voucher_v1_voucher_service_proto_goTypes = []any{
	(*CartItem)(nil),                         // 0: coupon.v1.CartItem
	(*CheckVoucherEligibilityRequest)(nil),   // 1: coupon.v1.CheckVoucherEligibilityRequest
	(*CheckVoucherEligibilityResponse)(nil),  // 2: coupon.v1.CheckVoucherEligibilityResponse
	(*ListAutoEligibleVouchersRequest)(nil),  // 3: coupon.v1.ListAutoEligibleVouchersRequest
	(*VoucherInfo)(nil),                      // 4: coupon.v1.VoucherInfo
	(*EligibleVoucherInfo)(nil),              // 5: coupon.v1.EligibleVoucherInfo
	(*ListAutoEligibleVouchersResponse)(nil), // 6: coupon.v1.ListAutoEligibleVouchersResponse
	(*v1.RequestMetadata)(nil),               // 7: common.v1.RequestMetadata
	(*timestamppb.Timestamp)(nil),            // 8: google.protobuf.Timestamp
	(*v1.ResponseMetadata)(nil),              // 9: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                  // 10: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),            // 11: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),           // 12: common.v1.HealthCheckResponse
}
var file_voucher_v1_voucher_service_proto_depIdxs = []int32{
	7,  // 0: coupon.v1.CheckVoucherEligibilityRequest.metadata:type_name -> common.v1.RequestMetadata
	8,  // 1: coupon.v1.CheckVoucherEligibilityRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	0,  // 2: coupon.v1.CheckVoucherEligibilityRequest.cart_items:type_name -> coupon.v1.CartItem
	9,  // 3: coupon.v1.CheckVoucherEligibilityResponse.metadata:type_name -> common.v1.ResponseMetadata
	10, // 4: coupon.v1.CheckVoucherEligibilityResponse.error:type_name -> common.v1.ServiceError
	7,  // 5: coupon.v1.ListAutoEligibleVouchersRequest.metadata:type_name -> common.v1.RequestMetadata
	8,  // 6: coupon.v1.ListAutoEligibleVouchersRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	0,  // 7: coupon.v1.ListAutoEligibleVouchersRequest.cart_items:type_name -> coupon.v1.CartItem
	4,  // 8: coupon.v1.EligibleVoucherInfo.voucher:type_name -> coupon.v1.VoucherInfo
	9,  // 9: coupon.v1.ListAutoEligibleVouchersResponse.metadata:type_name -> common.v1.ResponseMetadata
	5,  // 10: coupon.v1.ListAutoEligibleVouchersResponse.vouchers:type_name -> coupon.v1.EligibleVoucherInfo
	10, // 11: coupon.v1.ListAutoEligibleVouchersResponse.error:type_name -> common.v1.ServiceError
	1,  // 12: coupon.v1.VoucherService.CheckVoucherEligibility:input_type -> coupon.v1.CheckVoucherEligibilityRequest
	3,  // 13: coupon.v1.VoucherService.ListAutoEligibleVouchers:input_type -> coupon.v1.ListAutoEligibleVouchersRequest
	11, // 14: coupon.v1.VoucherService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	2,  // 15: coupon.v1.VoucherService.CheckVoucherEligibility:output_type -> coupon.v1.CheckVoucherEligibilityResponse
	6,  // 16: coupon.v1.VoucherService.ListAutoEligibleVouchers:output_type -> coupon.v1.ListAutoEligibleVouchersResponse
	12, // 17: coupon.v1.VoucherService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_voucher_v1_voucher_service_proto_init() }
func file_voucher_v1_voucher_service_proto_init() {
	if File_voucher_v1_voucher_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_voucher_v1_voucher_service_proto_rawDesc), len(file_voucher_v1_voucher_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_voucher_v1_voucher_service_proto_goTypes,
		DependencyIndexes: file_voucher_v1_voucher_service_proto_depIdxs,
		MessageInfos:      file_voucher_v1_voucher_service_proto_msgTypes,
	}.Build()
	File_voucher_v1_voucher_service_proto = out.File
	file_voucher_v1_voucher_service_proto_goTypes = nil
	file_voucher_v1_voucher_service_proto_depIdxs = nil
}
