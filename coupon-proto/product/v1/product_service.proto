syntax = "proto3";

package product.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1";

service ProductService {
  // Internal product management APIs used for service-to-service communication
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse);
  rpc GetProduct(GetProductRequest) returns (GetProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  rpc DeleteProduct(DeleteProductRequest) returns (DeleteProductResponse);
  rpc ListProducts(ListProductsRequest) returns (ListProductsResponse);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message Product {
  string id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  string category = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message CreateProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  string category = 5;
}

message CreateProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  Product product = 2;
  common.v1.ServiceError error = 3;
}

message GetProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string product_id = 2;
}

message GetProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  Product product = 2;
  common.v1.ServiceError error = 3;
}

message UpdateProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string product_id = 2;
  string name = 3;
  string description = 4;
  double price = 5;
  string category = 6;
}

message UpdateProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  Product product = 2;
  common.v1.ServiceError error = 3;
}

message DeleteProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string product_id = 2;
}

message DeleteProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool deleted = 2;
  common.v1.ServiceError error = 3;
}

message ListProductsRequest {
  common.v1.RequestMetadata metadata = 1;
  common.v1.PaginationRequest pagination = 2;
}

message ListProductsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Product products = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}
